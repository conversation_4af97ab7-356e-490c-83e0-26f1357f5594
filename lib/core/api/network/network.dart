import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/network_error_manager.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/aes_encryption.dart';
import 'package:gp_stock_app/core/utils/app_lifecycle_service.dart';
import 'package:gp_stock_app/core/utils/connectivity_util.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/core/services/http/network_reachability.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

import '../../../shared/app/extension/helper.dart';
import '../../../shared/constants/enums.dart';
import '../../utils/log.dart';
import 'endpoint/urls.dart';
import 'network_helper.dart';

@Deprecated('Use `Http().request<T>` instead')
class NetworkProvider {
  static final NetworkProvider _instance = NetworkProvider._internal();
  static Dio? _dio;

  static final Map<String, Response> _cache = {};

  // 域名拉取的防抖与冷却控制
  static bool _isFetchingHost = false;
  static DateTime? _lastFetchTime;
  static const Duration _fetchCooldown = Duration(seconds: 30);

  static final List<String> _excludedEndpoints = [
    ApiEndpoints.getGainDistribution,
    ApiEndpoints.getMarketPlate,
    ApiEndpoints.getPlateList,
    ApiEndpoints.getIndexList,
    ApiEndpoints.getStockInfo,
    ApiEndpoints.getKlineData,
    ApiEndpoints.getAccountInfo,
    ApiEndpoints.getContractSummary,
    // ApiEndpoints.getContractSummaryPage,
    ApiEndpoints.getOrderList,
    ApiEndpoints.getPositionList,
    ApiEndpoints.getMarketStatus,
  ];

  bool _shouldExcludeLogging(String path) => _excludedEndpoints.any((endpoint) => path.contains(endpoint));

  bool _shouldExcludeErrorMessage(String path) => _excludedEndpoints.any((endpoint) => path == endpoint);

  factory NetworkProvider({String? baseUrl}) {
    _dio ??= _createDio(baseUrl);
    return _instance;
  }

  NetworkProvider._internal() {
    // setup interceptors
    _dio!.interceptors.add(
      RetryInterceptor(
        dio: _dio!,
        logPrint: print,
        retries: 3,
        retryEvaluator: (error, attempt) {
          return error.type != DioExceptionType.cancel &&
              !(error.response?.statusCode == 403 ||
                  error.response?.statusCode == 401 ||
                  error.response?.statusCode == 404 ||
                  error.response?.statusCode == 400);
        },
      ),
    );
    _dio!.interceptors.add(
      InterceptorsWrapper(
        onRequest: _handleRequest,
        onResponse: _handleResponse,
        onError: _handleError,
      ),
    );
    // 配置 setup Keep-Alive
    (_dio!.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient()
        ..connectionTimeout = const Duration(seconds: 20)
        ..idleTimeout = const Duration(seconds: 60)
        ..maxConnectionsPerHost = 100;
      // client.findProxy = (uri) {
      //   return 'PROXY **************:8888';
      // };
      // client.badCertificateCallback = (cert, host, port) => true;
      return client;
    };
  }

  static Dio _createDio(String? baseUrl) {
    return Dio(BaseOptions(
      baseUrl: baseUrl ?? '${HostUtil().currentHost ?? Urls.baseUrl}/api',
      headers: {
        'Content-Type': 'application/json',
        if (kDebugMode) 'X-Skip-Encrypt': 1,
        'Accept-Language': 'en-US',
      },
      connectTimeout: const Duration(seconds: 20),
      receiveTimeout: const Duration(seconds: 20),
      sendTimeout: const Duration(seconds: 20),
      persistentConnection: true,
    ));
  }

  /// 运行时重置 BaseUrl，并同步更新 HostUtil 的 currentHost
  Future<void> resetBaseUrl(String newBaseUrl) async {
    // 更新运行中 Dio 的 baseUrl
    if (_dio != null) {
      _dio!.options.baseUrl = '$newBaseUrl/api';
    }

    LogD("🔁 动态重置 baseUrl => ${_dio?.options.baseUrl}");
  }

  Future<void> _handleRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    if (!_shouldExcludeLogging(options.path) && options.queryParameters.isNotEmpty) {
      LogD('Query Parameters = ${jsonEncode(options.queryParameters)}');
    }

    if (options.headers.containsKey('auth') && getIt.isRegistered<UserCubit>()) {
      final userState = getIt<UserCubit>().state;
      options.headers.remove('auth');

      if (!userState.isLogin) {
        return handler.reject(
          DioException(
            requestOptions: options,
            error: 'signInError'.tr(),
          ),
        );
      }
      options.headers['Authorization'] = userState.token;
    }

    if (!_shouldExcludeLogging(options.path)) {
      if (options.contentType?.contains('multipart/form-data') ?? false) {
        LogD('Request = ${options.data}');
      } else {
        LogD('Request = ${jsonEncode(options.data)}');
      }
    }
    handler.next(options);
  }

  void _handleResponse(Response response, ResponseInterceptorHandler handler) {
    if (!_shouldExcludeLogging(response.requestOptions.path)) {
      dynamic params;
      if (response.requestOptions.method == 'GET') {
        params = response.requestOptions.queryParameters;
      } else {
        params = response.requestOptions.data;
      }
      LogD(
        '✅ ${response.requestOptions.baseUrl} \n'
        '✅ ${response.requestOptions.path} \n'
        '✅ METHOD:${response.requestOptions.method} \n'
        '✅ HEADER:${jsonEncode(response.requestOptions.headers)} \n'
        '✅ STATUS_CODE:${jsonEncode(response.statusCode)} \n'
        '✅ PARAMS:${fmt(params, 1)} \n'
        '✅ RESPONSE_DATA:${jsonEncode(response.data)}',
      );
    }
    handler.next(response);
  }

  Future<void> _handleError(DioException error, ErrorInterceptorHandler handler) async {
    if (!_shouldExcludeLogging(error.requestOptions.path)) {
      dynamic params;
      if (error.response?.requestOptions.method == 'GET') {
        params = error.response?.requestOptions.queryParameters;
      } else {
        params = error.response?.requestOptions.data;
      }
      LogD(
        '❌ ${error.requestOptions.baseUrl} \n'
        '❌ ${error.requestOptions.path} \n'
        '❌ METHOD:${error.requestOptions.method} \n'
        '❌ HEADER:${jsonEncode(error.requestOptions.headers)} \n'
        '❌ STATUS_CODE:${jsonEncode(error.response?.statusCode)} \n'
        '❌ PARAMS:${fmt(params, 1)} \n'
        '❌ RESPONSE_DATA:${jsonEncode(error.response?.data)}',
      );
    }
    try {
      if (error.response?.statusCode == 401 && error.requestOptions.path != '/login') {
        await _handleUnauthorizedError();
      } else if (_isNetworkError(error) && !_shouldExcludeErrorMessage(error.requestOptions.path)) {
        if (kDebugMode) {
          NetworkErrorManager().showNetworkError(error: error);
        }
      }
    } catch (e) {
      LogE('Error handler failed: $e');
    }

    if (isServerUnreachable(error)) {
      _maybeFetchNewHost();
    }

    handler.next(
      DioException(
        requestOptions: error.requestOptions,
        response: error.response,
        error: Helper().errorMapping(error.response) ?? 'Unknown error occurred',
      ),
    );
  }

  Future<void> fetchNewHost() async {
    final url = await HostUtil().fetchHostFromOss();
    if (url != null) {
      NetworkProvider().resetBaseUrl(url);
      Http().resetBaseUrl(url);
    }
  }

  // 判定逻辑已抽取到 core/utils/network_reachability.dart

  void _maybeFetchNewHost() {
    if (_isFetchingHost) return;
    final now = DateTime.now();
    if (_lastFetchTime != null && now.difference(_lastFetchTime!) < _fetchCooldown) return;

    _isFetchingHost = true;
    fetchNewHost().whenComplete(() {
      _lastFetchTime = DateTime.now();
      _isFetchingHost = false;
    });
  }

  Future<void> _handleUnauthorizedError() async {
    GPEasyLoading.dismiss();
    Helper.logoutUser(needNav: false);
    NetworkHelper.handleMessage(
      'sessionExpired'.tr(),
      type: HandleTypes.customDialog,
      snackBarType: SnackBarType.error,
      dialogKey: 'unauthorized_error',
      onTap: () {
        getIt<NavigatorService>().pushNamedAndRemoveUntil(AppRouter.routeLogin);
      },
    );
  }

  Future<Response<T>> _makeRequest<T>(
    String method,
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = true,
  }) async {
    final cacheKey = _generateCacheKey(method, path, data ?? queryParameters ?? {});

    if (!force && _cache.containsKey(cacheKey) && method == 'GET') {
      return _cache[cacheKey]! as Response<T>;
    }

    try {
      // Set X-Skip-Encrypt header in debug mode
      if (kDebugMode && options != null) {
        options.headers = options.headers ?? {};
        options.headers!['X-Skip-Encrypt'] = 1;
      }

      // Skip request encryption for now (will be implemented later)
      // Just pass the original data without encryption
      dynamic encryptedData = data;

      final response = await _performRequest<T>(
        method,
        path,
        encryptedData,
        queryParameters,
        options,
        cancelToken,
        onSendProgress,
        onReceiveProgress,
      );

      // Only decrypt responses if X-Skip-Encrypt header was not set
      bool skipDecryption = options?.headers?['X-Skip-Encrypt'] == 1;

      if (!skipDecryption && response.data != null) {
        try {
          final decryptedData = AESEncryption().decryptResponse(response.data);
          response.data = decryptedData as T;
        } catch (e) {
          LogE('Error decrypting response: $e');
          // Continue with original response if decryption fails
        }
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        _cache[cacheKey] = response;
      }

      return response;
    } catch (error) {
      return Future.error(error);
    }
  }

  Future<Response<T>> _performRequest<T>(
    String method,
    String path,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
  ) async {
    final cleanData = Helper().removeNullValues(data ?? {});
    final cleanQueryParams = Helper().removeNullValues(queryParameters ?? {});

    switch (method) {
      case 'GET':
        return _dio!.get<T>(
          path,
          queryParameters: cleanQueryParams,
          options: options,
          cancelToken: cancelToken,
          onReceiveProgress: onReceiveProgress,
        );
      case 'POST':
        return _dio!.post<T>(
          path,
          data: cleanData,
          queryParameters: cleanQueryParams,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        );
      case 'PUT':
        return _dio!.put<T>(
          path,
          data: cleanData,
          queryParameters: cleanQueryParams,
          options: options,
          cancelToken: cancelToken,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress,
        );
      case 'DELETE':
        return _dio!.delete<T>(
          path,
          data: cleanData,
          queryParameters: cleanQueryParams,
          options: options,
          cancelToken: cancelToken,
        );
      case 'PATCH':
        return _dio!.patch<T>(
          path,
          data: cleanData,
          queryParameters: cleanQueryParams,
          options: options,
          cancelToken: cancelToken,
        );
      default:
        throw UnsupportedError('Unsupported HTTP method: $method');
    }
  }

  // Public API methods
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onReceiveProgress,
    bool force = true,
    bool isAuthRequired = false,
  }) async {
    return _makeRequest<T>(
      'GET',
      path,
      queryParameters: queryParameters,
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      onReceiveProgress: onReceiveProgress,
      force: force,
    );
  }

  Future<Response<T>> post<T>(
    String path, {
    Map<String, dynamic>? data,
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = true,
    bool isAuthRequired = false,
  }) async {
    return _makeRequest<T>(
      'POST',
      path,
      data: data ?? formData,
      queryParameters: queryParameters,
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
    );
  }

  Future<Response<T>> put<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool force = true,
    bool isAuthRequired = false,
  }) async {
    return _makeRequest<T>(
      'PUT',
      path,
      data: data,
      queryParameters: queryParameters,
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      force: force,
    );
  }

  Future<Response<T>> delete<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = true,
    bool isAuthRequired = false,
  }) async {
    return _makeRequest<T>(
      'DELETE',
      path,
      data: data,
      queryParameters: queryParameters,
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      force: force,
    );
  }

  Future<Response<T>> patch<T>(
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    bool force = true,
    bool isAuthRequired = false,
  }) async {
    return _makeRequest<T>(
      'PATCH',
      path,
      data: data,
      queryParameters: queryParameters,
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      force: force,
    );
  }

  Future<Response<T>> formData<T>(
    String path, {
    FormData? formData,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
    bool isAuthRequired = false,
  }) async {
    return _dio!.post<T>(
      path,
      data: formData,
      queryParameters: Helper().removeNullValues(queryParameters ?? {}),
      options: options ?? Options(headers: {if (isAuthRequired) 'auth': true}),
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }

  // Simplified retryRequest as it was using unnecessary Completer
  Future<Response<T>> retryRequest<T>(RequestOptions requestOptions) {
    return request<T>(requestOptions);
  }

  Future<Response<T>> request<T>(RequestOptions requestOptions) {
    return _dio!.request<T>(
      requestOptions.path,
      cancelToken: requestOptions.cancelToken,
      data: requestOptions.data,
      onReceiveProgress: requestOptions.onReceiveProgress,
      onSendProgress: requestOptions.onSendProgress,
      queryParameters: requestOptions.queryParameters,
      options: Options(
        method: requestOptions.method,
        sendTimeout: requestOptions.sendTimeout,
        receiveTimeout: requestOptions.receiveTimeout,
        extra: requestOptions.extra,
        headers: requestOptions.headers,
        responseType: requestOptions.responseType,
        contentType: requestOptions.contentType,
        validateStatus: requestOptions.validateStatus,
        receiveDataWhenStatusError: requestOptions.receiveDataWhenStatusError,
        followRedirects: requestOptions.followRedirects,
        maxRedirects: requestOptions.maxRedirects,
        persistentConnection: requestOptions.persistentConnection,
        requestEncoder: requestOptions.requestEncoder,
        responseDecoder: requestOptions.responseDecoder,
        listFormat: requestOptions.listFormat,
      ),
    );
  }

  String _generateCacheKey(String method, String url, dynamic data) => '$method|$url|${jsonEncode(data)}';

  /// Checks if the error is a network-related error
  bool _isNetworkError(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.connectionError ||
        error.type == DioExceptionType.badResponse ||
        error.response?.statusCode == 404 ||
        (error.toString().toLowerCase().contains('failed to parse the media type')) ||
        (error.toString().toLowerCase().contains('websocket')) ||
        (error.toString().toLowerCase().contains('no host specified')) ||
        (error.error != null &&
            (error.error.toString().toLowerCase().contains('socket') ||
                error.error.toString().toLowerCase().contains('connection') ||
                error.error.toString().toLowerCase().contains('network') ||
                error.error.toString().toLowerCase().contains('host') ||
                error.error.toString().toLowerCase().contains('dns')));
  }

  static void clearCache() => _cache.clear();
}

String fmt(dynamic json, int indent) {
  const encoder = JsonEncoder.withIndent('  ');
  return encoder.convert(json);
}
