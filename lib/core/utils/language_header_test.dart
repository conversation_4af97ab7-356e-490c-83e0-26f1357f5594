import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/core/utils/log.dart';

/// Utility class to test and demonstrate Accept-Language header functionality
class LanguageHeaderTest {
  /// Get Accept-Language header based on current locale
  static String getAcceptLanguageHeader() {
    try {
      final context = getIt<NavigatorService>().navigatorKey.currentContext;
      if (context != null) {
        final locale = context.locale;
        return '${locale.languageCode}-${locale.countryCode?.toUpperCase() ?? locale.languageCode.toUpperCase()}';
      }
    } catch (e) {
      LogD('Error getting current locale: $e');
    }
    // Default fallback
    return 'en-US';
  }

  /// Test function to demonstrate Accept-Language header changes
  static void testLanguageHeaders() {
    final currentHeader = getAcceptLanguageHeader();
    LogD('Current Accept-Language header: $currentHeader');
    
    // Log expected headers for different locales
    LogD('Expected headers for different locales:');
    LogD('- English (US): en-US');
    LogD('- Chinese (Simplified): zh-CN');
    LogD('- Chinese (Traditional): zh-TW');
  }

  /// Get expected Accept-Language header for a given locale
  static String getExpectedHeader(Locale locale) {
    return '${locale.languageCode}-${locale.countryCode?.toUpperCase() ?? locale.languageCode.toUpperCase()}';
  }

  /// Validate if the current header matches the expected format
  static bool validateCurrentHeader() {
    final currentHeader = getAcceptLanguageHeader();
    final validFormats = ['en-US', 'zh-CN', 'zh-TW'];
    return validFormats.contains(currentHeader);
  }
}
