import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

/// 追加保证金对话框
/// Add Margin Dialog
///
/// 通用的追加保证金对话框，支持从外部传入可用保证金数据流
/// Generic add margin dialog that supports external available margin data stream
///
/// 使用示例 Usage Examples:
///
/// 1. 基础用法（仅显示初始数据）Basic usage (show initial data only):
/// ```dart
/// await TradeAddMarginDialog(
///   context,
///   data: orderRecord,
///   onPressedSubmit: (amount) async {
///     // 处理提交逻辑
///   },
/// ).show();
/// ```
///
/// 2. 使用AccountScreenCubitV2的数据流 With AccountScreenCubitV2 streams:
/// ```dart
/// final cubit = context.read<AccountScreenCubitV2>();
/// cubit.updateCurrentSelectSpotOrder(orderRecord);
///
/// await TradeAddMarginDialog(
///   context,
///   data: orderRecord,
///   availableMarginStream: cubit.stream.map((state) =>
///     state.currentSelectSpotOrder?.availableMargin ?? 0.0),
///   onPressedSubmit: (amount) async {
///     // 处理提交逻辑
///   },
/// ).show();
///
/// cubit.updateCurrentSelectSpotOrder(null); // 清理
/// ```
///
/// 3. 使用自定义数据流 With custom streams:
/// ```dart
/// final StreamController<double> marginController = StreamController<double>();
///
/// // 外部定时刷新数据并推送到流中
/// Timer.periodic(Duration(seconds: 3), (timer) {
///   marginController.add(newMarginValue);
/// });
///
/// await TradeAddMarginDialog(
///   context,
///   data: orderRecord,
///   availableMarginStream: marginController.stream,
///   onPressedSubmit: (amount) async {
///     // 处理提交逻辑
///   },
/// ).show();
/// ```
class TradeAddMarginDialog {
  final BuildContext context;
  final FTradeAcctOrderRecords data;
  final Function(double amount) onPressedSubmit;

  /// 可用保证金数据流（可选）
  /// Available margin data stream (optional)
  final Stream<double>? availableMarginStream;

  TradeAddMarginDialog(
    this.context, {
    required this.data,
    required this.onPressedSubmit,
    this.availableMarginStream,
  });

  /// 显示追加保证金对话框
  /// Shows the add margin dialog
  ///
  /// 返回用户操作结果 Returns user action result
  Future<Map<String, dynamic>?> show() async {
    final textController = TextEditingController();

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => BlocSelector<UserCubit, UserState, double>(
        selector: (state) => state.accountInfo?.usableCash ?? 0,
        builder: (context, availableBalance) {
          return TradeAddMarginDialogContent(
            positionMarginAmount: data.marginAmount,
            initialAvailableMargin: data.availableMargin,
            availableBalance: availableBalance,
            textController: textController,
            onPressedSubmit: onPressedSubmit,
            availableMarginStream: availableMarginStream,
          );
        },
      ),
    );

    return result;
  }
}

class TradeAddMarginDialogContent extends StatefulWidget {
  final double positionMarginAmount;
  final double initialAvailableMargin;
  final double availableBalance;
  final TextEditingController textController;
  final Function(double amount) onPressedSubmit;

  /// 可用保证金数据流
  /// Available margin data stream
  final Stream<double>? availableMarginStream;

  const TradeAddMarginDialogContent({
    super.key,
    required this.positionMarginAmount,
    required this.initialAvailableMargin,
    required this.availableBalance,
    required this.textController,
    required this.onPressedSubmit,
    this.availableMarginStream,
  });

  @override
  State<TradeAddMarginDialogContent> createState() => _TradeAddMarginDialogContentState();
}

class _TradeAddMarginDialogContentState extends State<TradeAddMarginDialogContent> {
  bool isAgreeProtocol = false;
  bool isSubmitBtnLoading = false;

  StreamSubscription<double>? _marginSubscription;
  double _currentAvailableMargin = 0.0;

  @override
  void initState() {
    super.initState();
    _currentAvailableMargin = widget.initialAvailableMargin;

    // 监听可用保证金数据流
    // Listen to available margin stream
    _marginSubscription = widget.availableMarginStream?.listen((margin) {
      if (mounted) {
        setState(() {
          _currentAvailableMargin = margin;
        });
      }
    });
  }

  @override
  void dispose() {
    _marginSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              width: 347.gw,
              padding: EdgeInsets.fromLTRB(15.gw, 20.gw, 15.gw, 22.gw),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'marginCall'.tr(),
                        style: context.textTheme.regular,
                      ),
                    ],
                  ),
                  11.verticalSpace,
                  AmountRow(
                    title: 'position_margin'.tr(), // 持仓保证金
                    amount: widget.positionMarginAmount,
                  ),
                  8.verticalSpace,
                  AmountRow(
                    title: 'available_margin'.tr(), // 可用保证金
                    amount: _currentAvailableMargin,
                  ),
                  8.verticalSpace,
                  AmountRow(
                    title: 'available_balance'.tr(), // 可用金额
                    amount: widget.availableBalance,
                  ),
                  25.verticalSpace,
                  Text(
                    'contract_deposit'.tr(),
                    style: context.textTheme.regular.fs16.w500.copyWith(
                      color: context.colorTheme.textPrimary,
                    ),
                  ),
                  12.verticalSpace,
                  TextFieldWidget(
                    controller: widget.textController,
                    hintText: 'please_enter_amount'.tr(),
                    textInputType: TextInputType.numberWithOptions(decimal: true),
                    hintStyle: context.textTheme.regular.fs16.w500.copyWith(color: context.colorTheme.textTitle),
                    showBorderSide: false,
                  ),
                  12.verticalSpace,
                  _buildAgreementRow(context),
                  25.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CommonButton(
                          title: 'cancel'.tr(),
                          borderColor: Color(0xffD9D9D9),
                          style: CommonButtonStyle.outlined,
                          fontSize: 16,
                          onPressed: () async {
                            Navigator.of(context).pop();
                          },
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: CommonButton(
                          title: 'confirm'.tr(),
                          borderColor: context.theme.primaryColor,
                          color: context.theme.primaryColor,
                          fontSize: 16,
                          showLoading: isSubmitBtnLoading,
                          enable: isAgreeProtocol,
                          onPressed: () async {
                            FocusScope.of(context).unfocus();

                            final amount = double.tryParse(widget.textController.text) ?? 0;

                            if (amount != 0) {
                              setState(() {
                                isSubmitBtnLoading = true;
                              });
                              try {
                                await widget.onPressedSubmit.call(amount);
                              } finally {
                                if (mounted) {
                                  setState(() {
                                    isSubmitBtnLoading = false;
                                  });
                                }
                              }
                            } else {
                              GPEasyLoading.showToast('please_enter_margin'.tr()); // 请先设置'止盈/止损'价格
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void updateIsAgreeProtocol(bool value) {
    setState(() {
      isAgreeProtocol = value;
    });
  }

  Widget _buildAgreementRow(BuildContext context) {
    return Row(
      children: [
        CustomRadioButton(
          isSelected: isAgreeProtocol,
          onChange: (value) => updateIsAgreeProtocol(value),
        ),
        9.horizontalSpace,
        Text.rich(
          TextSpan(
            text: '${'readAndAgree'.tr()} ',
            style: context.textTheme.regular.fs10.copyWith(
              color: context.colorTheme.textRegular,
            ),
            children: [
              TextSpan(
                text: 'marginAgreement'.tr(),
                style: context.textTheme.regular.fs10.copyWith(
                  color: context.theme.primaryColor,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    showAppInfoBottomSheet(
                      context,
                      capitalA: '',
                      capitalB: '',
                      interestRate: '',
                      interestAmount: '',
                      period: 1,
                    );
                  },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
