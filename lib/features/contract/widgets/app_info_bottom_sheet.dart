import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_state.dart';
import 'package:gp_stock_app/features/profile/logic/mission_center/cubit/mission_activity_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/profile/profile_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AppInfoBottomSheet extends StatelessWidget {
  const AppInfoBottomSheet({
    super.key,
    required this.capitalA,
    required this.capitalB,
    required this.interestRate,
    required this.interestAmount,
    required this.period,
  });

  final String capitalA;
  final String capitalB;
  final String interestRate;
  final String interestAmount;
  final int period;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: BlocBuilder<AppInfoCubit, AppInfoState>(
        builder: (context, state) {
          if (state.status == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state.status == DataStatus.failed) {
            return Center(
              child: Text(
                state.error ?? 'Failed to load content',
                style: context.textTheme.stockRed,
              ),
            );
          } else if (state.status == DataStatus.success) {
            // Find the app info with ID 4
            final appInfo = state.appInfoList.firstWhere(
              (info) => info.type == 4,
              orElse: () => const AppInfoModel(),
            );

            if (appInfo.id == 0) {
              return Center(
                child: Text(
                  'Content not found',
                  style: context.textTheme.regular,
                ),
              );
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: Text(
                          appInfo.title,
                          style: context.textTheme.primary.fs18.w700,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                ),
                16.verticalSpace,
                Flexible(
                  child: SingleChildScrollView(
                    child: Html(
                      data: alteredHTML(
                        appInfo.content,
                        capitalA: capitalA,
                        capitalB: capitalB,
                        interestRate: interestRate,
                        interestAmount: interestAmount,
                        period: period,
                      ),
                    ),
                  ),
                ),
                16.verticalSpace,
              ],
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  /// Converts a number to Chinese characters
  String numberToChinese(String num) {
    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '萬', '億', '兆'];
    const chars = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];

    // Check if the input is a valid number string
    if (!RegExp(r'^\d+$').hasMatch(num)) return '';

    String result = '';
    int groupIndex = 0;

    while (num.isNotEmpty) {
      String section = num.length >= 4 ? num.substring(num.length - 4) : num;
      num = num.length >= 4 ? num.substring(0, num.length - 4) : '';

      String sectionStr = '';
      bool zero = false;

      for (int i = 0; i < section.length; i++) {
        final digit = int.parse(section[section.length - 1 - i]);
        if (digit == 0) {
          if (!zero) {
            zero = true;
            sectionStr = chars[0] + sectionStr;
          }
        } else {
          zero = false;
          sectionStr = chars[digit] + units[i] + sectionStr;
        }
      }

      // Remove trailing zeros
      sectionStr = sectionStr.replaceAll(RegExp(r'零+$'), '');
      // Merge multiple zeros
      sectionStr = sectionStr.replaceAll(RegExp(r'零+'), '零');

      if (sectionStr.isNotEmpty) {
        result = sectionStr + bigUnits[groupIndex] + result;
      }
      groupIndex++;
    }

    return result.isEmpty ? chars[0] : result;
  }

  String alteredHTML(
    String html, {
    required String capitalA,
    required String capitalB,
    required String interestRate,
    required String interestAmount,
    required int period,
  }) {
    final user = getIt<ProfileCubit>().state.userData;
    final serverDate = getIt<MissionActivityCubit>().state.serverDate ?? DateTime.now();
    final data = {
      '{{party_b}}': user?.realName ?? '',
      '{{contract_start_date}}': DateFormat('yyyy-MM-dd').format(serverDate),
      '{{contract_end_date}}': DateFormat('yyyy-MM-dd').format(serverDate.add(Duration(days: period))),
      '{{original_a_capital_1}}': capitalA,
      '{{original_a_capital_2}}': numberToChinese(capitalA),
      '{{original_b_capital_1}}': capitalB,
      '{{original_b_capital_2}}': numberToChinese(capitalB),
      '{{interest_rate}}': interestRate,
      '{{interest_amount}}': interestAmount,
      '{{party_mobile}}': user?.mobile ?? '',
    };
    data.forEach((key, value) {
      if (value.isEmpty) {
        html = html.replaceAll(key, '');
        return;
      }
      html = html.replaceAll(key, value);
    });

    return html;
  }
}

/// Shows the app info bottom sheet with content ID 4
void showAppInfoBottomSheet(
  BuildContext context, {
  required String capitalA,
  required String capitalB,
  required String interestRate,
  required String interestAmount,
  required int period,
}) {
  // Get the AppInfoCubit from the dependency injection
  final appInfoCubit = context.read<AppInfoCubit>();

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.75,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (_, scrollController) => BlocProvider.value(
        value: appInfoCubit,
        child: AppInfoBottomSheet(
          capitalA: capitalA,
          capitalB: capitalB,
          interestRate: interestRate,
          interestAmount: interestAmount,
          period: period,
        ),
      ),
    ),
  );
}
